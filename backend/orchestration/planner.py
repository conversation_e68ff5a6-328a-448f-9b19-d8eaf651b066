"""🧠 Orchestration Planning Logic

This module handles the **PLANNING phase** of the LangGraph multi-agent orchestration cycle.
It transforms the raw user query into a structured plan of executable tasks using LLM and RAG.

Responsibilities:
- Decompose the user request into discrete `Task` instances.
- Use RAG to gather context and guide planning.
- Infer task dependencies, types, and agent suggestions.
- Validate planning logic and ensure task plan consistency.

📦 This file defines:
- `OrchestrationPlanner`: Core planner that generates validated task plans from input.
- `Planner`: Utility class for creating, updating, and loading tasks during orchestration.
- `plan_with_state(...)`: State-aware function used by LangGraph nodes.
- `TaskSpec`, `PlanningResponse`: Pydantic models used to structure and validate LLM output.

🛠️ Core Functions:
- `generate_plan(...)`: Main planning routine (RAG + LLM + validation).
- `_generate_tasks_with_llm(...)`: Handles prompt construction, LLM call, and parsing.
- `_validate_task_plan(...)`: Validates task dependency graph and structure.
"""

import logging
import json
import re  # For JSON extraction from LLM responses
import uuid  # For generating unique task IDs

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import sys

from pydantic import BaseModel, Field

# Import orchestration components
from .state import Task, TaskStatus, TaskType, LangGraphState
from .db import save_task, load_tasks
from .utils.shared_services import ContextService, TaskValidator, ServiceInitializer
from .utils.error_handling import handle_orchestration_errors

# Import LLM components
from backend.llm import LLMAdapter

# Import RAG components
from backend.rag.knowledge_base import KnowledgeBaseService
from backend.rag.llm import RAGLLMAdapter

# Import configuration
from backend.app.config import get_settings

logger = logging.getLogger(__name__)


# Pydantic models for type safety
class TaskSpec(BaseModel):
    """Pydantic model for validating LLM-generated task specifications."""

    description: str = Field(..., description="Clear, actionable task description")
    task_type: str = Field(..., description="Task type from predefined list")
    dependencies: List[str] = Field(
        default_factory=list, description="List of task IDs this depends on"
    )
    estimated_complexity: str = Field(
        default="medium", description="Complexity: low|medium|high"
    )
    assigned_agent_suggestion: Optional[str] = Field(
        default=None, description="Suggested agent assignment"
    )


class PlanningResponse(BaseModel):
    """
    Intermediate schema for parsing and validating structured task planning output from the LLM.

    This model is used internally within the OrchestrationPlanner to:
    - Validate the LLM's planning output structure.
    - Convert raw JSON from the LLM into TaskSpec instances.
    - Extract the high-level analysis and task breakdown before instantiating actual Task objects.

    Note:
    - This is not used beyond the planning phase.
    - The coordinator and rest of the system operate solely on finalized Task objects.

    Fields:
    - analysis: High-level explanation of the user query and overall task plan.
    - tasks: A list of task specifications (TaskSpec), each representing a potential Task to be instantiated.
    """
    analysis: str = Field(
        ..., description="Brief analysis of the user's request and approach"
    )
    tasks: List[TaskSpec] = Field(..., description="List of task specifications")


class OrchestrationPlanner:
    """
    Orchestration planner that uses LLM reasoning and RAG context
    to generate structured task plans for multi-agent orchestration.

    LLM & RAG integrations:
    -> Use LLM for query analysis and task decomposition
    -> Leverage RAG for user-specific context
    -> Generate structured `Task` objects with `dependencies` and `types` methods
    """

    def __init__(
        self,
        llm_adapter: Optional[RAGLLMAdapter] = None,  # LLM adapter for reasoning (auto-initialized if None)
        knowledge_base_service: Optional[KnowledgeBaseService] = None,  # RAG service for context (auto-initialized if None)
        max_context_docs: int = 5,  # Max docs retrieved -> can and should be adjusted
        planning_temperature: float = 0.3,  # Lower temperature for deterministic planning
        # planning_max_tokens: int = 1000,  # Max tokens for planning response
    ):
        """
        Initialize the orchestration planner.

        Args:

        - llm_adapter: LLM adapter for reasoning (auto-initialized if None)
        - knowledge_base_service: RAG service for context (auto-initialized if None)
        - max_context_docs: Maximum documents to retrieve for context
        - planning_temperature: LLM temperature for planning (lower = more structured)
        # planning_max_tokens: Max tokens for planning response
        """
        # Store configuration
        self.max_context_docs = max_context_docs
        self.planning_temperature = planning_temperature
        # self.planning_max_tokens = planning_max_tokens
        self.settings = get_settings()

        # Initialize services
        self.llm_adapter = llm_adapter
        self.knowledge_base_service = knowledge_base_service
        
        # Initialize shared services
        self.context_service = ContextService(llm_adapter, knowledge_base_service)
        
        # Planning prompt templates
        self._planning_prompts = self._initialize_planning_prompts()

    @handle_orchestration_errors("OrchestrationPlanner")
    async def generate_plan(
        self, user_input: str, context: Optional[Dict[str, Any]] = None
    ) -> Tuple[List[Task], str]:
        """
        Generate a structured task plan based on user input and context.

        This is the main orchestration method that:
        1. Gathers relevant context from RAG
        2. Analyzes user intent with LLM
        3. Generates structured tasks with dependencies
        4. Validates and returns the plan

        Args:
            user_input: The raw user query
            context: Additional context (previous tasks, user preferences, etc.)

        Returns:
            Tuple of (List of Task objects representing the execution plan, Analysis string)
        """
        print("[PLANNER] generate_plan called for:", user_input)
        sys.stdout.flush()
        if context is None:
            context = {}

        start_time = datetime.now()
        logger.info(f"Starting plan generation for: {user_input[:100]}...")

        try:
            # Initialize services if needed
            await self._initialize_services()
            
            # Gather context
            rag_context = await self.context_service.gather_context(user_input, context)
            
            # Generate tasks using LLM reasoning
            tasks, analysis = await self._generate_tasks_with_llm(user_input, rag_context, context)
            
            # Log and print LLM output and parsed results
            logger.debug(f"Planning analysis: {analysis}")
            logger.debug(f"Generated {len(tasks)} tasks: {[t.description for t in tasks]}")
            for t in tasks:
                logger.debug(f"Task: {t.task_id}, Type: {t.task_type}, Agent: {t.assigned_agent}, Desc: {t.description}")
            print("\n[PLANNER] Planning analysis:", analysis)
            print("[PLANNER] Generated tasks:", [t.description for t in tasks])
            sys.stdout.flush()
            
            # Validate task plan using shared validator
            is_valid, error_msg = TaskValidator.validate_task_plan(tasks)
            if not is_valid:
                logger.warning(f"Task plan validation failed: {error_msg}")
                return self._generate_fallback_tasks(user_input, context), "Task plan validation failed"
            
            # Log planning metrics
            planning_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"Plan generation completed: {len(tasks)} tasks in {planning_time:.2f}s")
            
            return tasks, analysis
            
        except Exception as e:
            logger.exception(f"Error in plan generation: {e}")
            return self._generate_fallback_tasks(user_input, context), "Error in plan generation"

    async def _initialize_services(self):
        """
        Initialize LLM and RAG services using ServiceFactory.

        This method uses the ServiceFactory for clean separation of concerns.
        """
        if self.llm_adapter is None or self.knowledge_base_service is None:
            self.llm_adapter, self.knowledge_base_service = await ServiceInitializer.initialize_services(
                self.llm_adapter,
                self.knowledge_base_service,
                self.planning_temperature
            )

    def _initialize_planning_prompts(self) -> Dict[str, str]:
        """
        Initialize prompt templates for different planning scenarios.

        These prompts guide the LLM to generate structured, actionable task plans
        that align with our orchestration architecture.

        Returns:
            Dictionary of prompt templates keyed by scenario name
        """
        prompts = {
            "task_analysis": """
    You are an expert business orchestration planner. Analyze the user's request and break it down into structured tasks.

    USER REQUEST: {user_input}

    AVAILABLE CONTEXT:
    {context_summary}

    RELEVANT KNOWLEDGE:
    {rag_context}

    Your task is to create a structured plan with the following task types:
    - QUERY_DECOMPOSITION: Break down complex queries into sub-questions
    - REASONING: Analyze information and draw conclusions  
    - COMMUNICATION: Coordinate between different agents or stakeholders
    - TOOLS: Use specific tools (web search, document retrieval, calculations)
    - ACTION: Execute specific business actions
    - EVALUATION: Review and validate results

    REQUIREMENTS:
    1. Each task must have a clear, actionable description
    2. Identify dependencies between tasks (which tasks must complete before others)
    3. Assign appropriate task types
    4. Consider the business context and domain knowledge provided
    5. **Suggest 5-7 tasks for clarity and focus, but use more if truly needed for multi-agent collaboration.**

    OUTPUT FORMAT (JSON):
    {{
        "analysis": "Brief analysis of the user's request and approach",
        "tasks": [
            {{
                "description": "Clear, actionable task description",
                "task_type": "TASK_TYPE_FROM_LIST_ABOVE",
                "dependencies": ["list", "of", "task", "ids", "this", "depends", "on"],
                "estimated_complexity": "low|medium|high",
                "assigned_agent_suggestion": "co_ceo|finance|marketing|null"
            }}
        ]
    }}

    Generate a comprehensive but efficient plan:""",
            "context_summary": """
    Summarize the following context for planning purposes:

    PREVIOUS TASKS: {previous_tasks}
    USER PREFERENCES: {user_preferences}  
    CURRENT SESSION: {session_context}

    Provide a brief summary of relevant context that should influence task planning:""",
            "task_validation": """
    Review this task plan for logical consistency and completeness:

    PLAN: {task_plan}
    ORIGINAL REQUEST: {user_input}

    Check for:
    1. Missing critical steps
    2. Circular dependencies
    3. Appropriate task sequencing
    4. Resource requirements

    Provide validation feedback and suggest improvements:""",
        }

        return prompts

    async def _generate_tasks_with_llm(
        self,
        user_input: str,
        rag_context: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None,
    ) -> Tuple[List[Task], str]:
        """
        Use LLM reasoning to generate structured tasks based on user input and context.

        This method:
        1. Prepares context-rich prompts
        2. Calls LLM for task analysis and generation
        3. Parses and validates LLM response
        4. Creates Task objects with proper structure

        Args:
            user_input: The user's query
            rag_context: Context gathered from RAG system
            context: Additional orchestration context

        Returns:
            Tuple of (List of structured Task objects, Analysis string)
        """
        try:
            # Prepare context summary for LLM
            context_summary = self._prepare_context_summary(context)
            rag_summary = self._prepare_rag_summary(rag_context)

            # Build the planning prompt
            planning_prompt = self._planning_prompts["task_analysis"].format(
                user_input=user_input,
                context_summary=context_summary,
                rag_context=rag_summary,
            )

            # Call LLM for task planning
            logger.debug("Calling LLM for task planning analysis")

            messages = [
                {
                    "role": "system",
                    "content": "You are an expert business orchestration planner.",
                },
                {"role": "user", "content": planning_prompt},
            ]
            # Make LLM call and await the response
            llm_response = await self.llm_adapter.chat(
                messages=messages,
                temperature=self.planning_temperature,
                max_tokens=getattr(self.settings, "LLM_DEFAULT_MAX_TOKENS", 2048),
            )

            # Handle different LLM response formats (string vs dict)
            if isinstance(llm_response, dict):
                response_text = llm_response.get("content", str(llm_response))
            else:
                response_text = str(llm_response)

            logger.debug(
                f"Raw LLM planning response: {response_text}"
            )
            print("\n[PLANNER] Raw LLM planning response:\n", response_text)
            sys.stdout.flush()

            # Parse LLM response into structured tasks with analysis
            tasks, analysis = await self._parse_llm_response_to_tasks(
                response_text, user_input
            )

            logger.info(f"Generated {len(tasks)} tasks from LLM planning")
            return tasks, analysis

        except Exception as e:
            print("[PLANNER] Error parsing LLM response:", e)
            sys.stdout.flush()
            logger.exception(f"Error parsing LLM response: {e}")
            # Fallback to simple task generation
            return self._generate_fallback_tasks(
                user_input, context
            ), "Error in LLM task generation"

    def _prepare_context_summary(self, context: Optional[Dict[str, Any]]) -> str:
        """Prepare a human-readable context summary for LLM prompting."""
        if not context:
            return "No additional context provided."

        summary_parts = []

        if "previous_tasks" in context:
            summary_parts.append(
                f"Previous tasks: {len(context['previous_tasks'])} completed"
            )

        if "user_preferences" in context:
            prefs = context.get("user_preferences", {})
            if isinstance(prefs, dict):
                summary_parts.append(f"User preferences: {', '.join(prefs.keys())}")
            else:
                summary_parts.append(f"User preferences: {prefs}")

        if "session_context" in context:
            summary_parts.append(
                f"Session context: {context.get('session_context', 'None')}"
            )

        return "; ".join(summary_parts) if summary_parts else "No additional context."

    def _prepare_rag_summary(self, rag_context: Dict[str, Any]) -> str:
        """Prepare a summary of RAG context for LLM prompting."""
        if not rag_context.get("relevant_documents"):
            return "No relevant knowledge base documents found."

        summary_parts = []

        # Summarize document content
        docs = rag_context["relevant_documents"]
        summary_parts.append(f"Found {len(docs)} relevant documents")

        # Add key insights
        if rag_context.get("domain_insights"):
            summary_parts.append(
                f"Domain insights: {', '.join(rag_context['domain_insights'])}"
            )

        if rag_context.get("complexity_indicators"):
            summary_parts.append(
                f"Complexity factors: {', '.join(rag_context['complexity_indicators'])}"
            )

        if rag_context.get("suggested_agents"):
            summary_parts.append(
                f"Suggested agents: {', '.join(rag_context['suggested_agents'])}"
            )

        # Include top document excerpts
        top_docs = docs[:2]  # Include top 2 documents
        for i, doc in enumerate(top_docs, 1):
            content = doc.get("content", "")[:200]  # First 200 chars
            summary_parts.append(f"Document {i}: {content}...")

        return "\n".join(summary_parts)

    def _extract_largest_json(self, text: str) -> str:
        """Extract the largest valid JSON object from a string."""
        # Remove code block markers
        text = text.strip()
        if text.startswith('```json'):
            text = text[len('```json'):]
        if text.startswith('```'):
            text = text[len('```'):]
        if text.endswith('```'):
            text = text[:-3]
        text = text.strip()
        # Find the largest {...} block
        matches = list(re.finditer(r'\{', text))
        best_json = None
        for match in matches:
            start = match.start()
            for end in range(len(text), start, -1):
                try:
                    candidate = text[start:end]
                    json.loads(candidate)  # Validate JSON
                    best_json = candidate
                    break
                except Exception:
                    continue
            if best_json:
                break
        if not best_json:
            raise ValueError("No valid JSON object found in LLM output")
        return best_json

    async def _parse_llm_response_to_tasks(
        self, llm_response: str, user_input: str
    ) -> Tuple[List[Task], str]:
        """
        Parse LLM response into structured Task objects using Pydantic validation.

        This method handles:
        1. JSON extraction from LLM response
        2. Pydantic validation of task specifications
        3. Task object creation with unique IDs
        4. Dependency mapping and validation
        5. Error handling and fallbacks

        Args:
            llm_response: Raw response from LLM
            user_input: Original user input for fallback context

        Returns:
            Tuple of (List of validated Task objects, Analysis string)
        """
        try:
            # Extract the largest valid JSON object
            llm_response = self._extract_largest_json(llm_response)
            response_json = json.loads(llm_response)
            planning_response = PlanningResponse.model_validate(response_json)
            tasks = []
            task_id_mapping = {}
            for i, task_spec in enumerate(planning_response.tasks):
                temp_id = f"task-{i + 1}"
                agent = task_spec.assigned_agent_suggestion
                if agent:
                    agent = agent.strip().replace("_", " ").title().replace(" ", "")
                    if agent.lower() == "coceo" or agent.lower() == "co ceo":
                        agent = "CoCEO"
                    elif agent.lower() == "finance":
                        agent = "Finance"
                    elif agent.lower() == "marketing":
                        agent = "Marketing"
                task = Task(
                    description=task_spec.description,
                    task_type=self._parse_task_type(task_spec.task_type),
                    status=TaskStatus.PENDING,
                    dependencies=set(),
                    assigned_agent=agent,
                )
                tasks.append(task)
                task_id_mapping[temp_id] = task.task_id

            # Second pass: Map dependencies using validated task specs
            for i, (task, task_spec) in enumerate(zip(tasks, planning_response.tasks)):
                mapped_dependencies = set()

                for dep in task_spec.dependencies:
                    if dep in task_id_mapping:
                        mapped_dependencies.add(task_id_mapping[dep])
                    else:
                        logger.warning(f"Invalid dependency reference: {dep}")

                task.dependencies = mapped_dependencies

            # Validate task plan using shared validator
            is_valid, error_msg = TaskValidator.validate_task_plan(tasks)
            if is_valid:
                logger.info(f"Successfully parsed {len(tasks)} tasks from LLM response")
                return tasks, planning_response.analysis
            else:
                logger.warning(f"Task plan validation failed: {error_msg}")
                return self._generate_fallback_tasks(
                    user_input, {}
                ), "Task plan validation failed"

        except Exception as e:
            logger.exception(f"Error parsing LLM response to tasks: {e}")
            return self._generate_fallback_tasks(
                user_input, {}
            ), f"Parsing error: {str(e)}"

    def _parse_task_type(self, task_type_str: str) -> TaskType:
        """Parse task type string to TaskType enum with fallback."""
        try:
            return TaskType(task_type_str.upper())
        except ValueError:
            logger.warning(f"Unknown task type: {task_type_str}, defaulting to ACTION")
            return TaskType.ACTION



    def _generate_fallback_tasks(
        self, user_input: str, _context: Optional[Dict[str, Any]] = None
    ) -> List[Task]:
        """Generate a simple fallback task plan when LLM parsing fails."""
        logger.info("Generating fallback task plan")
        return [
            Task(
                task_id=f"fallback-{uuid.uuid4().hex[:8]}",
                description=f"Analyze and respond to: {user_input}",
                task_type=TaskType.REASONING,
                status=TaskStatus.PENDING,
                assigned_agent="CoCEO",
                dependencies=set(),
            )
        ]


# Global planner instance for backward compatibility
_global_planner: Optional[OrchestrationPlanner] = None


async def plan(user_input: str, context: Optional[Dict[str, Any]] = None) -> List[Task]:
    """
    Backward compatibility function for existing code.

    This function maintains the original API while using the enhanced planner internally.
    It creates a global planner instance on first use and reuses it for subsequent calls.

    Args:
        user_input: The user's query
        context: Additional context for planning

    Returns:
        List of Task objects representing the execution plan
    """
    global _global_planner

    # Initialize global planner on first use
    if _global_planner is None:
        logger.info("Initializing global orchestration planner")
        _global_planner = OrchestrationPlanner()

    # Use the enhanced planner (return only tasks for backward compatibility)
    tasks, _analysis = await _global_planner.generate_plan(user_input, context)
    return tasks


# Backward compatibility alias
async def generate_plan(
    user_input: str, context: Optional[Dict[str, Any]] = None
) -> List[Task]:
    """Backward compatibility alias for plan()."""
    return await plan(user_input, context)


# State-aware planning function for LangGraph integration
async def plan_with_state(user_input: str, state: LangGraphState, llm_adapter: LLMAdapter) -> List[Task]:
    """
    Generate a task plan using the provided state and LLM adapter.
    Args:
        user_input: The user's input prompt
        state: The current orchestration state
        llm_adapter: The LLM adapter to use for planning
    Returns:
        List of Task objects representing the execution plan
    """
    planner = OrchestrationPlanner(
        llm_adapter=llm_adapter,
        planning_temperature=0.3
    )
    tasks, analysis = await planner.generate_plan(user_input, state.context)
    state.context["planning_analysis"] = analysis
    return tasks


class Planner:
    """Planner for orchestration."""
    def __init__(self):
        pass

    async def create_task(
        self,
        state: LangGraphState,
        description: str,
        task_type: str,
        dependencies: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Task:
        """Create a new task and save it to the database."""
        task = Task(
            task_id=f"task_{uuid.uuid4().hex[:8]}",  # Generate a proper UUID
            description=description,
            task_type=task_type,
            status=TaskStatus.PENDING,
            dependencies=set(dependencies or []),
            metadata=metadata,
        )
        if state.state_id:
            task_id = await save_task(task, state.state_id)
            task.task_id = task_id
        state.tasks.append(task)
        await state.save()
        return task

    async def update_task(
        self,
        state: LangGraphState,
        task_id: str,
        status: Optional[str] = None,
        assigned_agent: Optional[str] = None,
        output: Optional[Dict[str, Any]] = None,
    ) -> Optional[Task]:
        """Update a task and save changes to the database."""
        task = next((t for t in state.tasks if t.task_id == task_id), None)
        if not task:
            return None
        if status:
            task.status = status
        if assigned_agent:
            task.assigned_agent = assigned_agent
        if output:
            task.output = output
        if state.state_id:
            await save_task(task, state.state_id)
        await state.save()
        return task

    async def load_tasks(self, state: LangGraphState) -> List[Task]:
        """Load tasks from the database."""
        if state.state_id:
            return await load_tasks(state.state_id)
        return state.tasks
