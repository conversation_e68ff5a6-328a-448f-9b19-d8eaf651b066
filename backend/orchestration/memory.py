"""
A shared memory interface to enrich context across steps.
- Feeds data into `state.context`, including:
  -> Historical task traces.
  -> RAG-retrieved relevant information.
  -> Agent/system memory.
- Used by planner and agents to reason and personalize behavior.
"""

from __future__ import annotations

import uuid
import asyncio
from enum import Enum
from typing import Optional, List, Dict, Any, Union, Set, Tuple
from datetime import datetime, timezone, timedelta
from typing import TYPE_CHECKING

from pydantic import BaseModel, Field
import logging
logger = logging.getLogger(__name__)

if TYPE_CHECKING:
    from .state import OrchestrationStep, LangGraphState
    from ..rag import KnowledgeBaseService

class MemoryUnits:
    """Represents a unit of memory in the orchestration system."""
    def __init__(
        self,
        memory_id: str,
        timestamp: datetime,
        current_step: 'OrchestrationStep',
        content: str,
        agent: str,
        task_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        embedding: Optional[List[float]] = None,
    ):
        self.memory_id = memory_id
        self.timestamp = timestamp
        self.current_step = current_step
        self.content = content
        self.agent = agent
        self.task_id = task_id
        self.metadata = metadata or {}
        self.tags = tags or []
        self.embedding = embedding

class GlobalMemory:
    """Global memory management for orchestration."""
    def __init__(
        self,
        knowledge_base: Optional['KnowledgeBaseService'] = None,
        cache_ttl: int = 300  # 5 minutes cache TTL
    ):
        self.knowledge_base = knowledge_base
        self.memory_units: List[MemoryUnits] = []
        self._cache: Dict[str, Tuple[datetime, List[MemoryUnits]]] = {}
        self._cache_ttl = cache_ttl

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached results are still valid."""
        if cache_key not in self._cache:
            return False
        timestamp, _ = self._cache[cache_key]
        return (datetime.now() - timestamp).total_seconds() < self._cache_ttl

    async def add_memory(
        self,
        content: str,
        agent: str,
        current_step: 'OrchestrationStep',
        task_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        embedding: Optional[List[float]] = None,
        state_id: Optional[str] = None,
    ) -> str:
        """
        Add a new memory unit to the global memory.
        If state_id is provided, the memory is persisted to the database.
        """
        try:
            memory = MemoryUnits(
                memory_id=str(uuid.uuid4()),
                timestamp=datetime.now(timezone.utc),
                current_step=current_step,
                content=content,
                agent=agent,
                task_id=task_id,
                metadata=metadata,
                tags=tags,
                embedding=embedding
            )

            if state_id:
                from backend.orchestration.db import save_memory
                memory_id = await save_memory(memory, state_id, embedding)
                memory.memory_id = memory_id

            self.memory_units.append(memory)
            
            # Invalidate relevant caches
            self._invalidate_caches(agent, task_id, tags)
            
            return memory.memory_id
        except Exception as e:
            logger.error(f"Failed to add memory: {e}")
            raise

    def _invalidate_caches(self, agent: Optional[str] = None, task_id: Optional[str] = None, tags: Optional[List[str]] = None):
        """Invalidate caches based on new memory addition."""
        cache_keys_to_invalidate = []
        
        for cache_key in self._cache.keys():
            if agent and f"agent:{agent}" in cache_key:
                cache_keys_to_invalidate.append(cache_key)
            elif task_id and f"task:{task_id}" in cache_key:
                cache_keys_to_invalidate.append(cache_key)
            elif tags:
                for tag in tags:
                    if f"tag:{tag}" in cache_key:
                        cache_keys_to_invalidate.append(cache_key)
                        break

        for key in cache_keys_to_invalidate:
            del self._cache[key]

    async def search_memory(
        self,
        query: str,
        state_id: Optional[str] = None,
        limit: int = 10,
        search_type: str = "hybrid"  # "vector", "text", "hybrid"
    ) -> List[MemoryUnits]:
        """
        Comprehensive memory search using PostgreSQL and vector similarity.

        Args:
            query: Search query
            state_id: Optional state ID to filter by
            limit: Maximum number of results
            search_type: Type of search ("vector", "text", "hybrid")

        Returns:
            List of matching memory units
        """
        try:
            results = []

            if search_type in ["vector", "hybrid"]:
                # Vector similarity search
                try:
                    from backend.orchestration.db import search_memory_by_vector
                    from backend.rag.embeddings import get_embedding_model

                    # Generate embedding for query
                    embedding_model = get_embedding_model()
                    query_embedding = await embedding_model.embed_query(query)

                    # Search by vector similarity
                    vector_results = await search_memory_by_vector(query_embedding, limit=limit)

                    # Convert search results to MemoryUnits
                    for result in vector_results:
                        doc = result["document"]
                        from backend.orchestration.state import OrchestrationStep

                        memory = MemoryUnits(
                            memory_id=doc["id"],
                            timestamp=datetime.fromisoformat(doc["metadata"]["created_at"]) if doc["metadata"].get("created_at") else datetime.now(),
                            current_step=OrchestrationStep(doc["metadata"]["current_step"]),
                            content=doc["content"],
                            agent=doc["metadata"]["agent"],
                            task_id=doc["metadata"]["task_id"],
                            metadata=doc["metadata"].get("memory_metadata", {}),
                            tags=doc["metadata"].get("tags", []),
                            embedding=None
                        )
                        results.append(memory)

                    logger.debug(f"Vector search returned {len(vector_results)} results")
                except Exception as e:
                    logger.error(f"Vector search failed: {e}")

            # Filter by state_id if provided
            if state_id:
                results = [m for m in results if hasattr(m, 'state_id') and m.state_id == state_id]

            # Sort by relevance/timestamp and limit
            results.sort(key=lambda x: x.timestamp, reverse=True)
            return results[:limit]

        except Exception as e:
            logger.error(f"Memory search failed: {e}")
            return []

    async def delete_memory(self, memory_id: str, state_id: Optional[str] = None) -> bool:
        """
        Delete a memory unit from PostgreSQL and in-memory storage.

        Args:
            memory_id: ID of the memory to delete
            state_id: Optional state ID for additional validation

        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            # Delete from PostgreSQL
            from backend.app.core.db.database import get_async_db_session
            from sqlalchemy import text

            async with get_async_db_session() as db:
                if state_id:
                    result = await db.execute(
                        text("DELETE FROM orchestration_memory WHERE id = :memory_id AND state_id = :state_id"),
                        {"memory_id": memory_id, "state_id": state_id}
                    )
                else:
                    result = await db.execute(
                        text("DELETE FROM orchestration_memory WHERE id = :memory_id"),
                        {"memory_id": memory_id}
                    )
                await db.commit()

                deleted_count = result.rowcount
                logger.debug(f"Deleted {deleted_count} memory records from PostgreSQL")

            # Delete from in-memory storage
            self.memory_units = [m for m in self.memory_units if m.memory_id != memory_id]

            # Invalidate caches
            self._cache.clear()

            return deleted_count > 0

        except Exception as e:
            logger.error(f"Failed to delete memory {memory_id}: {e}")
            return False

    async def update_memory(
        self,
        memory_id: str,
        content: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        state_id: Optional[str] = None
    ) -> bool:
        """
        Update a memory unit in PostgreSQL and in-memory storage.

        Args:
            memory_id: ID of the memory to update
            content: New content (optional)
            metadata: New metadata (optional)
            tags: New tags (optional)
            state_id: Optional state ID for additional validation

        Returns:
            True if update was successful, False otherwise
        """
        try:
            # Update in PostgreSQL
            from backend.app.core.db.database import get_async_db_session
            from sqlalchemy import text
            import json

            update_fields = []
            params = {"memory_id": memory_id}

            if content is not None:
                update_fields.append("content = :content")
                params["content"] = content

            if metadata is not None:
                update_fields.append("memory_metadata = :metadata")
                params["metadata"] = json.dumps(metadata)

            if tags is not None:
                update_fields.append("tags = :tags")
                params["tags"] = json.dumps(tags)

            if not update_fields:
                return True  # Nothing to update

            query = f"UPDATE orchestration_memory SET {', '.join(update_fields)} WHERE id = :memory_id"
            if state_id:
                query += " AND state_id = :state_id"
                params["state_id"] = state_id

            async with get_async_db_session() as db:
                result = await db.execute(text(query), params)
                await db.commit()

                updated_count = result.rowcount
                logger.debug(f"Updated {updated_count} memory records in PostgreSQL")

            # Update in-memory storage
            for memory in self.memory_units:
                if memory.memory_id == memory_id:
                    if content is not None:
                        memory.content = content
                    if metadata is not None:
                        memory.metadata = metadata
                    if tags is not None:
                        memory.tags = tags
                    break

            # Invalidate caches
            self._cache.clear()

            return updated_count > 0

        except Exception as e:
            logger.error(f"Failed to update memory {memory_id}: {e}")
            return False

    async def retrieve_memory(
        self,
        query: Optional[str] = None,
        state_id: Optional[str] = None,
        limit: int = 10,
        agent: Optional[str] = None,
        task_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
        force_refresh: bool = False
    ) -> List[MemoryUnits]:
        """
        Retrieve memory units with advanced filtering and caching.
        
        Args:
            query: Optional search query for semantic search
            state_id: Optional state ID for database retrieval
            limit: Maximum number of results to return
            agent: Filter by agent
            task_id: Filter by task ID
            tags: Filter by tags
            force_refresh: Force cache refresh
            
        Returns:
            List of matching memory units
        """
        # Generate cache key
        cache_key = f"mem:{query or 'all'}:{agent or 'all'}:{task_id or 'all'}:{tags or 'all'}"
        
        # Check cache first
        if not force_refresh and self._is_cache_valid(cache_key):
            return self._cache[cache_key][1]

        try:
            results = []

            # Primary retrieval: PostgreSQL database
            if state_id:
                try:
                    from backend.orchestration.db import load_memory
                    db_memories = await load_memory(state_id, limit=limit * 2)  # Get more for filtering
                    results.extend(db_memories)
                    logger.debug(f"Retrieved {len(db_memories)} memories from PostgreSQL")
                except Exception as e:
                    logger.error(f"PostgreSQL memory retrieval failed: {e}")

            # Secondary retrieval: Vector search if query provided
            if query and len(results) < limit:
                try:
                    from backend.orchestration.db import search_memory_by_vector
                    from backend.rag.embeddings import get_embedding_model

                    # Generate embedding for query
                    embedding_model = get_embedding_model()
                    query_embedding = await embedding_model.embed_query(query)

                    # Search by vector similarity
                    vector_results = await search_memory_by_vector(query_embedding, limit=limit)

                    # Convert search results to MemoryUnits
                    for result in vector_results:
                        doc = result["document"]
                        # Import here to avoid circular imports
                        from backend.orchestration.state import OrchestrationStep

                        memory = MemoryUnits(
                            memory_id=doc["id"],
                            timestamp=datetime.fromisoformat(doc["metadata"]["created_at"]) if doc["metadata"].get("created_at") else datetime.now(),
                            current_step=OrchestrationStep(doc["metadata"]["current_step"]),
                            content=doc["content"],
                            agent=doc["metadata"]["agent"],
                            task_id=doc["metadata"]["task_id"],
                            metadata=doc["metadata"].get("memory_metadata", {}),
                            tags=doc["metadata"].get("tags", []),
                            embedding=None  # Don't load full embedding
                        )
                        results.append(memory)

                    logger.debug(f"Retrieved {len(vector_results)} memories from vector search")
                except Exception as e:
                    logger.error(f"Vector search failed: {e}")

            # Fallback: In-memory storage (for backwards compatibility)
            if not results and self.memory_units:
                results = self.memory_units
                logger.debug(f"Using {len(results)} in-memory memories as fallback")

            # Apply filters
            if agent:
                results = [m for m in results if m.agent == agent]
            if task_id:
                results = [m for m in results if m.task_id == task_id]
            if tags:
                results = [m for m in results if any(tag in m.tags for tag in tags)]

            # Sort by timestamp and limit
            results.sort(key=lambda x: x.timestamp, reverse=True)
            results = results[:limit]

            # Update cache
            self._cache[cache_key] = (datetime.now(), results)
            
            return results

        except Exception as e:
            logger.error(f"Memory retrieval failed: {e}")
            return []

    async def inject_into_context(self, state: 'LangGraphState') -> 'LangGraphState':
        """
        Inject relevant memory into the LangGraphState context.

        Args:
            state: Current LangGraphState

        Returns:
            Updated LangGraphState with memory context injected
        """
        try:
            # Get recent memories for context from PostgreSQL
            recent_memories = await self.retrieve_memory(
                state_id=state.id if hasattr(state, 'id') else None,
                limit=10,
                query=None
            )

            # Format memories for context
            memory_context = []
            for memory in recent_memories:
                memory_context.append({
                    "timestamp": memory.timestamp.isoformat(),
                    "agent": memory.agent,
                    "task_id": memory.task_id,
                    "content": memory.content[:200] + "..." if len(memory.content) > 200 else memory.content,
                    "tags": memory.tags,
                    "metadata": memory.metadata
                })

            # Inject into state context
            if "memory_context" not in state.context:
                state.context["memory_context"] = []

            state.context["memory_context"] = memory_context

            logger.debug(f"[Memory] Injected {len(memory_context)} memories into state context")
            return state
        except Exception as e:
            logger.error(f"Failed to inject memory into context: {e}")
            return state

    async def get_memory_statistics(self, state_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get comprehensive memory statistics from PostgreSQL.

        Args:
            state_id: Optional state ID to filter statistics

        Returns:
            Dictionary containing memory statistics
        """
        try:
            from backend.app.core.db.database import get_async_db_session
            from sqlalchemy import text

            stats = {
                "total_memories": 0,
                "memories_by_agent": {},
                "memories_by_step": {},
                "memories_with_embeddings": 0,
                "average_content_length": 0,
                "oldest_memory": None,
                "newest_memory": None,
                "cache_size": len(self._cache),
                "in_memory_count": len(self.memory_units)
            }

            async with get_async_db_session() as db:
                # Basic counts
                base_query = "FROM orchestration_memory"
                where_clause = ""
                params = {}

                if state_id:
                    where_clause = " WHERE state_id = :state_id"
                    params["state_id"] = state_id

                # Total count
                result = await db.execute(
                    text(f"SELECT COUNT(*) {base_query}{where_clause}"),
                    params
                )
                stats["total_memories"] = result.scalar()

                # Count with embeddings
                embedding_where = " AND embedding IS NOT NULL" if where_clause else " WHERE embedding IS NOT NULL"
                result = await db.execute(
                    text(f"SELECT COUNT(*) {base_query}{where_clause}{embedding_where}"),
                    params
                )
                stats["memories_with_embeddings"] = result.scalar()

                # Average content length
                result = await db.execute(
                    text(f"SELECT AVG(LENGTH(content)) {base_query}{where_clause}"),
                    params
                )
                avg_length = result.scalar()
                stats["average_content_length"] = float(avg_length) if avg_length else 0

                # Memories by agent
                result = await db.execute(
                    text(f"SELECT agent, COUNT(*) {base_query}{where_clause} GROUP BY agent"),
                    params
                )
                stats["memories_by_agent"] = dict(result.fetchall())

                # Memories by orchestration step
                result = await db.execute(
                    text(f"SELECT current_step, COUNT(*) {base_query}{where_clause} GROUP BY current_step"),
                    params
                )
                stats["memories_by_step"] = dict(result.fetchall())

                # Oldest and newest memories
                result = await db.execute(
                    text(f"SELECT MIN(created_at), MAX(created_at) {base_query}{where_clause}"),
                    params
                )
                oldest, newest = result.fetchone()
                stats["oldest_memory"] = oldest.isoformat() if oldest else None
                stats["newest_memory"] = newest.isoformat() if newest else None

            logger.debug(f"Generated memory statistics: {stats['total_memories']} total memories")
            return stats

        except Exception as e:
            logger.error(f"Failed to get memory statistics: {e}")
            return {"error": str(e)}

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the memory system.

        Returns:
            Dictionary containing health check results
        """
        try:
            health = {
                "status": "healthy",
                "checks": {},
                "errors": [],
                "warnings": []
            }

            # Check PostgreSQL connectivity
            try:
                from backend.app.core.db.database import get_async_db_session
                from sqlalchemy import text

                async with get_async_db_session() as db:
                    await db.execute(text("SELECT 1"))
                health["checks"]["postgresql_connection"] = "pass"
            except Exception as e:
                health["checks"]["postgresql_connection"] = "fail"
                health["errors"].append(f"PostgreSQL connection failed: {e}")
                health["status"] = "unhealthy"

            # Check memory table accessibility
            try:
                stats = await self.get_memory_statistics()
                if "error" in stats:
                    health["checks"]["memory_table_access"] = "fail"
                    health["errors"].append(f"Memory table access failed: {stats['error']}")
                    health["status"] = "unhealthy"
                else:
                    health["checks"]["memory_table_access"] = "pass"
                    health["checks"]["total_memories"] = stats["total_memories"]
            except Exception as e:
                health["checks"]["memory_table_access"] = "fail"
                health["errors"].append(f"Memory table access failed: {e}")
                health["status"] = "unhealthy"

            # Check cache health
            cache_size = len(self._cache)
            health["checks"]["cache_size"] = cache_size
            if cache_size > 1000:  # Arbitrary threshold
                health["warnings"].append(f"Large cache size: {cache_size} entries")
                if health["status"] == "healthy":
                    health["status"] = "degraded"

            # Check in-memory storage
            memory_count = len(self.memory_units)
            health["checks"]["in_memory_count"] = memory_count
            if memory_count > 10000:  # Arbitrary threshold
                health["warnings"].append(f"Large in-memory storage: {memory_count} entries")
                if health["status"] == "healthy":
                    health["status"] = "degraded"

            return health

        except Exception as e:
            return {
                "status": "unhealthy",
                "checks": {},
                "errors": [f"Health check failed: {e}"],
                "warnings": []
            }