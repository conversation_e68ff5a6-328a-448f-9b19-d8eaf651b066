"""
A shared memory interface to enrich context across steps.
- Feeds data into `state.context`, including:
  -> Historical task traces.
  -> RAG-retrieved relevant information.
  -> Agent/system memory.
- Used by planner and agents to reason and personalize behavior.
"""

from __future__ import annotations

import uuid
import asyncio
from enum import Enum
from typing import Optional, List, Dict, Any, Union, Set, Tuple
from datetime import datetime, timezone, timedelta
from typing import TYPE_CHECKING

from pydantic import BaseModel, Field
import logging
logger = logging.getLogger(__name__)

if TYPE_CHECKING:
    from .state import OrchestrationStep, LangGraphState
    from ..rag import KnowledgeBaseService

class MemoryUnits:
    """Represents a unit of memory in the orchestration system."""
    def __init__(
        self,
        memory_id: str,
        timestamp: datetime,
        current_step: 'OrchestrationStep',
        content: str,
        agent: str,
        task_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        embedding: Optional[List[float]] = None,
    ):
        self.memory_id = memory_id
        self.timestamp = timestamp
        self.current_step = current_step
        self.content = content
        self.agent = agent
        self.task_id = task_id
        self.metadata = metadata or {}
        self.tags = tags or []
        self.embedding = embedding

class GlobalMemory:
    """Global memory management for orchestration."""
    def __init__(
        self,
        knowledge_base: Optional['KnowledgeBaseService'] = None,
        cache_ttl: int = 300  # 5 minutes cache TTL
    ):
        self.knowledge_base = knowledge_base
        self.memory_units: List[MemoryUnits] = []
        self._cache: Dict[str, Tuple[datetime, List[MemoryUnits]]] = {}
        self._cache_ttl = cache_ttl

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached results are still valid."""
        if cache_key not in self._cache:
            return False
        timestamp, _ = self._cache[cache_key]
        return (datetime.now() - timestamp).total_seconds() < self._cache_ttl

    async def add_memory(
        self,
        content: str,
        agent: str,
        current_step: 'OrchestrationStep',
        task_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        embedding: Optional[List[float]] = None,
        state_id: Optional[str] = None,
    ) -> str:
        """
        Add a new memory unit to the global memory.
        If state_id is provided, the memory is persisted to the database.
        """
        try:
            memory = MemoryUnits(
                memory_id=str(uuid.uuid4()),
                timestamp=datetime.now(timezone.utc),
                current_step=current_step,
                content=content,
                agent=agent,
                task_id=task_id,
                metadata=metadata,
                tags=tags,
                embedding=embedding
            )

            if state_id:
                from backend.orchestration.db import save_memory
                memory_id = await save_memory(memory, state_id, embedding)
                memory.memory_id = memory_id

            self.memory_units.append(memory)
            
            # Invalidate relevant caches
            self._invalidate_caches(agent, task_id, tags)
            
            return memory.memory_id
        except Exception as e:
            logger.error(f"Failed to add memory: {e}")
            raise

    def _invalidate_caches(self, agent: Optional[str] = None, task_id: Optional[str] = None, tags: Optional[List[str]] = None):
        """Invalidate caches based on new memory addition."""
        cache_keys_to_invalidate = []
        
        for cache_key in self._cache.keys():
            if agent and f"agent:{agent}" in cache_key:
                cache_keys_to_invalidate.append(cache_key)
            elif task_id and f"task:{task_id}" in cache_key:
                cache_keys_to_invalidate.append(cache_key)
            elif tags:
                for tag in tags:
                    if f"tag:{tag}" in cache_key:
                        cache_keys_to_invalidate.append(cache_key)
                        break

        for key in cache_keys_to_invalidate:
            del self._cache[key]

    async def retrieve_memory(
        self,
        query: Optional[str] = None,
        state_id: Optional[str] = None,
        limit: int = 10,
        agent: Optional[str] = None,
        task_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
        force_refresh: bool = False
    ) -> List[MemoryUnits]:
        """
        Retrieve memory units with advanced filtering and caching.
        
        Args:
            query: Optional search query for semantic search
            state_id: Optional state ID for database retrieval
            limit: Maximum number of results to return
            agent: Filter by agent
            task_id: Filter by task ID
            tags: Filter by tags
            force_refresh: Force cache refresh
            
        Returns:
            List of matching memory units
        """
        # Generate cache key
        cache_key = f"mem:{query or 'all'}:{agent or 'all'}:{task_id or 'all'}:{tags or 'all'}"
        
        # Check cache first
        if not force_refresh and self._is_cache_valid(cache_key):
            return self._cache[cache_key][1]

        try:
            results = []
            
            # If query provided and knowledge base available, do semantic search
            if query and self.knowledge_base:
                try:
                    async with asyncio.timeout(30):  # 30 second timeout
                        rag_results = await self.knowledge_base.search(
                            query=query,
                            limit=limit,
                            search_type="hybrid"
                        )
                        
                        # Convert RAG results to memory units
                        for result in rag_results:
                            # Import here to avoid circular imports
                            from backend.orchestration.state import OrchestrationStep

                            memory = MemoryUnits(
                                memory_id=result.get("id", str(uuid.uuid4())),
                                timestamp=datetime.fromisoformat(result.get("timestamp", datetime.now().isoformat())),
                                current_step=OrchestrationStep(result.get("current_step", "PLANNING")),
                                content=result["content"],
                                agent=result.get("agent", "system"),
                                task_id=result.get("task_id"),
                                metadata=result.get("metadata", {}),
                                tags=result.get("tags", []),
                                embedding=result.get("embedding")
                            )
                            results.append(memory)
                except asyncio.TimeoutError:
                    logger.error("RAG search timed out")
                except Exception as e:
                    logger.error(f"RAG search failed: {e}")

            # If no RAG results or no query, fall back to memory units
            if not results:
                results = self.memory_units

            # Apply filters
            if agent:
                results = [m for m in results if m.agent == agent]
            if task_id:
                results = [m for m in results if m.task_id == task_id]
            if tags:
                results = [m for m in results if any(tag in m.tags for tag in tags)]

            # Sort by timestamp and limit
            results.sort(key=lambda x: x.timestamp, reverse=True)
            results = results[:limit]

            # Update cache
            self._cache[cache_key] = (datetime.now(), results)
            
            return results

        except Exception as e:
            logger.error(f"Memory retrieval failed: {e}")
            return []

    def inject_into_context(self, state: 'LangGraphState') -> 'LangGraphState':
        """
        Inject relevant memory into the LangGraphState context.

        Args:
            state: Current LangGraphState

        Returns:
            Updated LangGraphState with memory context injected
        """
        try:
            # Get recent memories for context
            recent_memories = self.retrieve_memory(
                limit=10,
                query=None
            )

            # Format memories for context
            memory_context = []
            for memory in recent_memories:
                memory_context.append({
                    "timestamp": memory.timestamp.isoformat(),
                    "agent": memory.agent,
                    "task_id": memory.task_id,
                    "content": memory.content[:200] + "..." if len(memory.content) > 200 else memory.content,
                    "tags": memory.tags,
                    "metadata": memory.metadata
                })

            # Inject into state context
            if "memory_context" not in state.context:
                state.context["memory_context"] = []

            state.context["memory_context"] = memory_context

            logger.debug(f"[Memory] Injected {len(memory_context)} memories into state context")
            return state
        except Exception as e:
            logger.error(f"Failed to inject memory into context: {e}")
            return state