# Memory Persistence System Documentation

## Overview

The Memory Persistence System provides a robust, scalable solution for storing and retrieving orchestration memories using PostgreSQL with pgvector for semantic search capabilities. This system enables cross-session continuity, intelligent caching, and comprehensive error handling.

## Architecture

### Core Components

1. **GlobalMemory Class** (`backend/orchestration/memory.py`)
   - Main interface for memory operations
   - Handles session management and persistence
   - Implements intelligent caching with hash-based keys
   - Provides graceful fallbacks for database connectivity issues

2. **Database Layer** (`backend/orchestration/db.py`)
   - PostgreSQL integration with pgvector support
   - Vector similarity search capabilities
   - Optimized queries with proper indexing

3. **Memory Units** (`backend/orchestration/memory.py`)
   - Structured memory representation
   - Metadata and tagging support
   - Embedding storage for semantic search

### Database Schema

#### orchestration_memory Table
```sql
CREATE TABLE orchestration_memory (
    id VARCHAR(64) PRIMARY KEY,
    state_id VARCHAR(64) NOT NULL,
    content TEXT NOT NULL,
    agent VARCHAR(64) NOT NULL,
    task_id VARCHAR(64),
    current_step VARCHAR(32) NOT NULL,
    memory_metadata JSONB,
    tags JSONB,
    embedding VECTOR(768),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Key Indexes
- `idx_orchestration_memory_state_id` - Fast state-based queries
- `idx_orchestration_memory_agent` - Agent-specific filtering
- `idx_orchestration_memory_embedding` - Vector similarity search (IVFFlat)
- `idx_orchestration_memory_content_fts` - Full-text search
- Composite indexes for common query patterns

## Features

### 1. Session Management
```python
# Initialize a memory session
memory = GlobalMemory(auto_persist=True, session_state_id="my-session")
await memory.initialize_session("my-session", load_existing=True)

# Add memories that automatically persist
memory_id = await memory.add_memory(
    content="Important decision made",
    agent="coordinator",
    current_step=OrchestrationStep.PLANNING,
    metadata={"priority": "high"},
    tags=["decision", "planning"]
)
```

### 2. Cross-Session Persistence
- Memories automatically persist to PostgreSQL
- Session initialization loads existing memories
- Seamless continuity across application restarts

### 3. Intelligent Caching
- Hash-based cache keys for deterministic caching
- Automatic cache invalidation on external changes
- Database version tracking for consistency

### 4. Vector Search
```python
# Semantic search using embeddings
results = await memory.search_memory(
    query="machine learning decisions",
    state_id="my-session",
    limit=10,
    search_type="vector"
)
```

### 5. Advanced Filtering
```python
# Filter by multiple criteria
memories = await memory.retrieve_memory(
    state_id="my-session",
    agent="coordinator",
    tags=["important"],
    limit=20
)
```

### 6. Error Handling and Fallbacks
- Graceful degradation when database is unavailable
- Structured logging with operation tracking
- Health checks and monitoring capabilities

## Usage Examples

### Basic Memory Operations

```python
from backend.orchestration.memory import GlobalMemory
from backend.orchestration.state import OrchestrationStep

# Initialize memory system
memory = GlobalMemory(auto_persist=True)
await memory.initialize_session("session-123")

# Add a memory
memory_id = await memory.add_memory(
    content="User requested financial analysis",
    agent="finance-agent",
    current_step=OrchestrationStep.TASK_EXECUTION,
    task_id="task-456",
    metadata={"user_id": "user-789", "priority": "high"},
    tags=["finance", "analysis", "user-request"]
)

# Retrieve memories
recent_memories = await memory.retrieve_memory(
    state_id="session-123",
    limit=10
)

# Search semantically
search_results = await memory.search_memory(
    query="financial analysis requests",
    state_id="session-123",
    search_type="hybrid"
)
```

### Context Injection

```python
# Inject memory context into orchestration state
updated_state = await memory.inject_into_context(current_state)
# Now current_state.context["memory_context"] contains relevant memories
```

### Health Monitoring

```python
# Check system health
health = await memory.health_check()
print(f"Status: {health['status']}")
print(f"Errors: {health['errors']}")

# Get detailed statistics
stats = await memory.get_memory_statistics("session-123")
print(f"Total memories: {stats['total_memories']}")
print(f"Memories by agent: {stats['memories_by_agent']}")
```

## Configuration

### Environment Variables

```bash
# Database connection
DATABASE_URL=postgresql://user:pass@localhost:5432/dbname

# Memory system settings
MEMORY_CACHE_TTL=300  # Cache TTL in seconds (default: 5 minutes)
MEMORY_AUTO_PERSIST=true  # Auto-persist memories to database
MEMORY_MAX_CONTENT_SIZE=100000  # Max content size in bytes

# Vector search settings
EMBEDDING_MODEL=all-MiniLM-L6-v2  # HuggingFace model for embeddings
VECTOR_SEARCH_LISTS=100  # IVFFlat lists parameter
```

### Initialization Options

```python
memory = GlobalMemory(
    cache_ttl=300,  # Cache TTL in seconds
    auto_persist=True,  # Auto-persist to PostgreSQL
    session_state_id="default-session"  # Default session ID
)
```

## Performance Considerations

### Indexing Strategy
- Vector indexes use IVFFlat for optimal performance
- Composite indexes for common query patterns
- Full-text search indexes for content queries

### Caching
- Hash-based cache keys prevent cache pollution
- Automatic invalidation on external changes
- Configurable TTL for different use cases

### Memory Management
- Configurable content size limits
- Automatic cleanup of old cache entries
- In-memory fallback for database outages

## Error Handling

### Exception Types
- `MemoryOperationError` - General memory operation failures
- `DatabaseConnectionError` - Database connectivity issues
- `MemoryValidationError` - Input validation failures

### Logging
- Structured logging with operation IDs
- Performance timing for all operations
- Detailed error context and stack traces

### Fallback Strategies
- In-memory storage when database unavailable
- Graceful degradation of search capabilities
- Automatic retry mechanisms with exponential backoff

## Monitoring and Observability

### Health Checks
```python
health = await memory.health_check()
# Returns: status, checks, errors, warnings
```

### Statistics
```python
stats = await memory.get_memory_statistics()
# Returns: total_memories, memories_by_agent, cache_size, etc.
```

### Logging Integration
- Compatible with structured logging systems
- Operation tracking with unique IDs
- Performance metrics and timing data

## Security Considerations

### Input Validation
- Content size limits (default: 100KB)
- Agent identifier validation
- SQL injection prevention through parameterized queries

### Access Control
- State-based isolation
- Agent-based filtering
- Metadata sanitization

### Data Protection
- Secure storage of sensitive information
- Configurable retention policies
- Audit trail capabilities

## Troubleshooting

### Common Issues

1. **Database Connection Failures**
   - Check DATABASE_URL configuration
   - Verify PostgreSQL and pgvector installation
   - Review network connectivity

2. **Vector Search Not Working**
   - Ensure pgvector extension is installed
   - Check embedding model availability
   - Verify vector index creation

3. **Performance Issues**
   - Review index usage with EXPLAIN ANALYZE
   - Adjust cache TTL settings
   - Consider memory content size limits

### Debug Mode
```python
import logging
logging.getLogger('backend.orchestration.memory').setLevel(logging.DEBUG)
```

## API Reference

### GlobalMemory Class Methods

#### Session Management
- `initialize_session(state_id, load_existing=True)` - Initialize memory session
- `persist_session(state_id=None)` - Persist all memories to database

#### Memory Operations
- `add_memory(content, agent, current_step, **kwargs)` - Add new memory
- `retrieve_memory(query=None, state_id=None, **filters)` - Retrieve memories
- `search_memory(query, state_id=None, search_type="hybrid")` - Semantic search
- `update_memory(memory_id, **updates)` - Update existing memory
- `delete_memory(memory_id, state_id=None)` - Delete memory

#### Utility Methods
- `inject_into_context(state)` - Inject memories into orchestration state
- `get_memory_statistics(state_id=None)` - Get comprehensive statistics
- `health_check()` - Perform system health check
- `invalidate_external_changes(state_id=None)` - Check for external changes

### Error Classes
- `MemoryOperationError` - Base exception for memory operations
- `DatabaseConnectionError` - Database connectivity issues
- `MemoryValidationError` - Input validation failures

## Migration Guide

See `migration_guide.md` for detailed instructions on migrating from previous versions.
