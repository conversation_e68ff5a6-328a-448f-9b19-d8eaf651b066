# Memory Persistence System Migration Guide

## Overview

This guide provides step-by-step instructions for migrating to the new PostgreSQL-based memory persistence system. The migration includes database schema updates, code changes, and data transformation procedures.

## Prerequisites

### System Requirements
- PostgreSQL 12+ with pgvector extension
- Python 3.8+ with required dependencies
- Alembic for database migrations
- Backup of existing data (if any)

### Dependency Installation
```bash
# Install pgvector extension in PostgreSQL
# Ubuntu/Debian:
sudo apt-get install postgresql-14-pgvector

# macOS with Homebrew:
brew install pgvector

# Or compile from source:
git clone https://github.com/pgvector/pgvector.git
cd pgvector
make
sudo make install
```

### Python Dependencies
```bash
# Install required packages
uv add psycopg2-binary
uv add pgvector
uv add sentence-transformers
uv add alembic
```

## Migration Steps

### Step 1: Database Preparation

#### 1.1 Enable pgvector Extension
```sql
-- Connect to your database and enable pgvector
CREATE EXTENSION IF NOT EXISTS vector;

-- Verify installation
SELECT * FROM pg_extension WHERE extname = 'vector';
```

#### 1.2 Backup Existing Data
```bash
# Create a backup of your existing database
pg_dump -h localhost -U username -d database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# Or backup specific tables if you have existing memory data
pg_dump -h localhost -U username -d database_name -t existing_memory_table > memory_backup.sql
```

### Step 2: Run Database Migrations

#### 2.1 Check Current Migration Status
```bash
cd backend
alembic current
alembic history
```

#### 2.2 Run New Migrations
```bash
# Apply all pending migrations
alembic upgrade head

# Verify migration success
alembic current
```

#### 2.3 Verify Schema Creation
```sql
-- Check that all tables were created
\dt orchestration_*

-- Verify vector column
\d orchestration_memory

-- Check indexes
\di orchestration_*
```

### Step 3: Data Migration (If Applicable)

#### 3.1 Migrate from In-Memory Storage
If you were using in-memory storage, no data migration is needed. The new system will start fresh.

#### 3.2 Migrate from Previous Database Schema
```python
# Example migration script for existing data
import asyncio
from backend.orchestration.memory import GlobalMemory
from backend.orchestration.state import OrchestrationStep

async def migrate_existing_data():
    """Migrate data from old schema to new schema."""
    
    # Initialize new memory system
    memory = GlobalMemory(auto_persist=True)
    
    # Example: Migrate from old table structure
    from backend.app.core.db.database import get_async_db_session
    from sqlalchemy import text
    
    async with get_async_db_session() as db:
        # Fetch old data
        result = await db.execute(text("""
            SELECT id, content, agent, created_at, metadata 
            FROM old_memory_table 
            ORDER BY created_at
        """))
        
        old_memories = result.fetchall()
        
        # Migrate each memory
        for old_memory in old_memories:
            await memory.add_memory(
                content=old_memory.content,
                agent=old_memory.agent,
                current_step=OrchestrationStep.PLANNING,  # Default step
                metadata=old_memory.metadata,
                state_id="migrated-session"
            )
        
        print(f"Migrated {len(old_memories)} memories")

# Run migration
asyncio.run(migrate_existing_data())
```

### Step 4: Configuration Updates

#### 4.1 Environment Variables
Update your `.env` file:
```bash
# Database connection (update if needed)
DATABASE_URL=postgresql://user:password@localhost:5432/database_name

# Memory system configuration
MEMORY_CACHE_TTL=300
MEMORY_AUTO_PERSIST=true
MEMORY_MAX_CONTENT_SIZE=100000

# Embedding configuration
EMBEDDING_MODEL=all-MiniLM-L6-v2
VECTOR_SEARCH_LISTS=100
```

#### 4.2 Application Configuration
Update your application initialization:
```python
# Old initialization (if applicable)
# memory = SimpleMemory()

# New initialization
from backend.orchestration.memory import GlobalMemory

memory = GlobalMemory(
    auto_persist=True,
    cache_ttl=300,
    session_state_id="default-session"
)

# Initialize session on startup
await memory.initialize_session("your-session-id", load_existing=True)
```

### Step 5: Code Updates

#### 5.1 Update Memory Usage Patterns

**Before:**
```python
# Old pattern (if applicable)
memory.add_memory_simple(content, agent)
memories = memory.get_memories()
```

**After:**
```python
# New pattern with enhanced features
memory_id = await memory.add_memory(
    content=content,
    agent=agent,
    current_step=current_step,
    task_id=task_id,
    metadata=metadata,
    tags=tags
)

memories = await memory.retrieve_memory(
    state_id=state_id,
    agent=agent,
    limit=10
)
```

#### 5.2 Update Search Operations

**Before:**
```python
# Simple text search (if applicable)
results = memory.search(query)
```

**After:**
```python
# Enhanced semantic search
results = await memory.search_memory(
    query=query,
    state_id=state_id,
    search_type="hybrid",  # "vector", "text", or "hybrid"
    limit=10
)
```

#### 5.3 Add Error Handling

```python
from backend.orchestration.memory import (
    MemoryOperationError, 
    DatabaseConnectionError,
    MemoryValidationError
)

try:
    memory_id = await memory.add_memory(...)
except MemoryValidationError as e:
    logger.error(f"Invalid memory input: {e}")
except DatabaseConnectionError as e:
    logger.error(f"Database connection failed: {e}")
    # Handle graceful degradation
except MemoryOperationError as e:
    logger.error(f"Memory operation failed: {e}")
```

### Step 6: Testing and Validation

#### 6.1 Run Integration Tests
```bash
cd backend
python tests_memory_comprehensive.py
python tests_memory_postgresql.py
```

#### 6.2 Verify Data Integrity
```python
# Check memory statistics
stats = await memory.get_memory_statistics()
print(f"Total memories: {stats['total_memories']}")
print(f"Memories by agent: {stats['memories_by_agent']}")

# Perform health check
health = await memory.health_check()
print(f"System status: {health['status']}")
```

#### 6.3 Test Vector Search
```python
# Test semantic search functionality
results = await memory.search_memory(
    query="test semantic search",
    search_type="vector",
    limit=5
)
print(f"Vector search returned {len(results)} results")
```

### Step 7: Performance Optimization

#### 7.1 Index Optimization
```sql
-- Check index usage
EXPLAIN ANALYZE SELECT * FROM orchestration_memory 
WHERE state_id = 'test' AND agent = 'test-agent';

-- Monitor vector search performance
EXPLAIN ANALYZE SELECT * FROM orchestration_memory 
ORDER BY embedding <-> '[0.1,0.2,...]' LIMIT 10;
```

#### 7.2 Cache Configuration
```python
# Adjust cache settings based on usage patterns
memory = GlobalMemory(
    cache_ttl=600,  # Increase for better performance
    auto_persist=True
)
```

## Rollback Procedures

### Emergency Rollback

#### 1. Database Rollback
```bash
# Rollback to previous migration
alembic downgrade -1

# Or rollback to specific revision
alembic downgrade <revision_id>
```

#### 2. Application Rollback
```python
# Temporarily disable auto-persistence
memory = GlobalMemory(auto_persist=False)

# Use in-memory fallback
memories = memory.memory_units  # Direct access to in-memory storage
```

#### 3. Data Recovery
```bash
# Restore from backup if needed
psql -h localhost -U username -d database_name < backup_file.sql
```

## Troubleshooting

### Common Migration Issues

#### 1. pgvector Extension Not Found
```bash
# Check if extension is available
SELECT * FROM pg_available_extensions WHERE name = 'vector';

# Install if missing (see Prerequisites section)
```

#### 2. Migration Fails Due to Existing Data
```sql
-- Check for conflicting data
SELECT table_name FROM information_schema.tables 
WHERE table_name LIKE 'orchestration_%';

-- Rename conflicting tables if needed
ALTER TABLE existing_table RENAME TO existing_table_backup;
```

#### 3. Vector Index Creation Fails
```sql
-- Check vector column data
SELECT COUNT(*) FROM orchestration_memory WHERE embedding IS NOT NULL;

-- Recreate index if needed
DROP INDEX IF EXISTS idx_orchestration_memory_embedding;
CREATE INDEX idx_orchestration_memory_embedding 
ON orchestration_memory USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);
```

#### 4. Performance Issues After Migration
```sql
-- Update table statistics
ANALYZE orchestration_memory;
ANALYZE orchestration_states;
ANALYZE orchestration_tasks;

-- Check for missing indexes
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats 
WHERE tablename LIKE 'orchestration_%';
```

### Getting Help

#### 1. Enable Debug Logging
```python
import logging
logging.getLogger('backend.orchestration.memory').setLevel(logging.DEBUG)
logging.getLogger('backend.orchestration.db').setLevel(logging.DEBUG)
```

#### 2. Health Check Diagnostics
```python
health = await memory.health_check()
print(f"Detailed health report: {health}")
```

#### 3. Database Diagnostics
```bash
# Run database health check
python -c "
from backend.app.core.db.database import check_database_integrity
result = check_database_integrity()
print(f'Database status: {result}')
"
```

## Post-Migration Checklist

- [ ] All migrations applied successfully
- [ ] pgvector extension enabled and working
- [ ] Integration tests passing
- [ ] Vector search functionality verified
- [ ] Performance benchmarks acceptable
- [ ] Error handling tested
- [ ] Monitoring and logging configured
- [ ] Backup procedures updated
- [ ] Documentation updated
- [ ] Team training completed

## Support

For additional support or questions about the migration:

1. Check the troubleshooting section above
2. Review the comprehensive documentation in `memory_persistence_system.md`
3. Run the diagnostic scripts provided
4. Check application logs for detailed error information
