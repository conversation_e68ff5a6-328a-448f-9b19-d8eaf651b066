#!/usr/bin/env python3
"""
Simple test for memory database operations without complex imports.
"""
import asyncio
import sys
import os
from datetime import datetime, timezone

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

async def test_simple_memory():
    """Test basic memory operations."""
    print("🧪 Testing Simple Memory Operations")
    print("=" * 50)
    
    try:
        # Test async database connection
        from backend.app.core.db.database import get_async_db_session
        from sqlalchemy import text
        
        print("\n1. Testing async database connection...")
        async with get_async_db_session() as db:
            result = await db.execute(text("SELECT 1"))
            print(f"   ✅ Async connection works: {result.scalar()}")
        
        # Test creating a state first
        print("\n2. Testing state creation...")
        async with get_async_db_session() as db:
            await db.execute(text("""
                INSERT INTO orchestration_states (id, user_input, context, current_step)
                VALUES ('simple-test-state', 'Simple test input', '{}', 'PLANNING')
                ON CONFLICT (id) DO NOTHING
            """))
            await db.commit()
            print("   ✅ State created successfully")
        
        # Test memory creation
        print("\n3. Testing memory creation...")
        async with get_async_db_session() as db:
            await db.execute(text("""
                INSERT INTO orchestration_memory (id, state_id, content, agent, current_step, memory_metadata, tags)
                VALUES ('simple-test-memory', 'simple-test-state', 'Simple test content', 'test-agent', 'PLANNING', '{}', '[]')
                ON CONFLICT (id) DO NOTHING
            """))
            await db.commit()
            print("   ✅ Memory created successfully")
        
        # Test memory retrieval
        print("\n4. Testing memory retrieval...")
        async with get_async_db_session() as db:
            result = await db.execute(text("""
                SELECT content, agent FROM orchestration_memory 
                WHERE id = 'simple-test-memory'
            """))
            row = result.fetchone()
            if row:
                print(f"   ✅ Memory retrieved: {row[0][:30]}... by {row[1]}")
            else:
                print("   ❌ Memory not found")
                return False
        
        # Test vector embedding update
        print("\n5. Testing vector embedding...")
        test_embedding = [0.1] * 768
        vector_str = f"[{','.join(str(x) for x in test_embedding)}]"
        
        async with get_async_db_session() as db:
            await db.execute(text("""
                UPDATE orchestration_memory 
                SET embedding = :embedding 
                WHERE id = 'simple-test-memory'
            """), {"embedding": vector_str})
            await db.commit()
            print("   ✅ Vector embedding updated successfully")
        
        # Test vector search
        print("\n6. Testing vector search...")
        async with get_async_db_session() as db:
            result = await db.execute(text("""
                SELECT content, 1 - (embedding <=> :query_embedding) AS score
                FROM orchestration_memory
                WHERE embedding IS NOT NULL
                ORDER BY embedding <-> :query_embedding
                LIMIT 1
            """), {"query_embedding": vector_str})
            row = result.fetchone()
            if row:
                print(f"   ✅ Vector search works: score = {row[1]:.4f}")
            else:
                print("   ⚠️ No vector results found")
        
        # Cleanup
        print("\n7. Cleaning up...")
        async with get_async_db_session() as db:
            await db.execute(text("DELETE FROM orchestration_memory WHERE id = 'simple-test-memory'"))
            await db.execute(text("DELETE FROM orchestration_states WHERE id = 'simple-test-state'"))
            await db.commit()
            print("   ✅ Cleanup completed")
        
        print("\n🎉 All simple memory operations passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_simple_memory())
    sys.exit(0 if success else 1)
