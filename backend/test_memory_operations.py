#!/usr/bin/env python3
"""
Test script for memory database operations.
"""
import asyncio
import sys
import os
import logging
from datetime import datetime, timezone

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

# Import directly to avoid circular import issues
from backend.orchestration.memory import MemoryUnits, GlobalMemory
from backend.orchestration.state import OrchestrationStep, LangGraphState
from backend.orchestration.db import save_memory, load_memory, search_memory_by_vector, save_state
from backend.app.core.db.database import check_database_integrity

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_memory_operations():
    """Test all memory database operations."""
    print("🧪 Testing Memory Database Operations")
    print("=" * 50)
    
    # 1. Database Health Check
    print("\n1. Database Health Check...")
    health = check_database_integrity()
    print(f"   Status: {health['overall_status']}")
    if health['errors']:
        print(f"   Errors: {health['errors']}")
        return False
    
    # 2. Test Memory Creation
    print("\n2. Testing Memory Creation...")
    test_memory = MemoryUnits(
        memory_id="test-memory-001",
        timestamp=datetime.now(timezone.utc),
        current_step=OrchestrationStep.PLANNING,
        content="This is a test memory for database operations validation.",
        agent="test-agent",
        task_id="test-task-001",
        metadata={"test": True, "priority": "high"},
        tags=["test", "validation", "memory"],
        embedding=None
    )
    print(f"   Created memory: {test_memory.memory_id}")
    
    # 3. Test State Creation (required for memory foreign key)
    print("\n3. Testing State Creation...")
    try:
        test_state = LangGraphState(
            id="test-state-001",
            user_input="Test user input for memory operations",
            context={},
            coordinator_plan_id=None,
            tasks=[],
            current_task_id=None,
            current_step=OrchestrationStep.PLANNING
        )

        test_state_id = await save_state(test_state)
        print(f"   Created state with ID: {test_state_id}")
    except Exception as e:
        print(f"   ❌ State creation failed: {e}")
        return False

    # 4. Test Memory Saving
    print("\n4. Testing Memory Saving...")
    try:
        test_embedding = [0.1] * 768  # Test embedding vector

        saved_id = await save_memory(test_memory, test_state_id, test_embedding)
        print(f"   Saved memory with ID: {saved_id}")
    except Exception as e:
        print(f"   ❌ Save failed: {e}")
        return False
    
    # 5. Test Memory Loading
    print("\n5. Testing Memory Loading...")
    try:
        loaded_memories = await load_memory(test_state_id, limit=5)
        print(f"   Loaded {len(loaded_memories)} memories")

        if loaded_memories:
            memory = loaded_memories[0]
            print(f"   First memory: {memory.content[:50]}...")
            print(f"   Agent: {memory.agent}")
            print(f"   Step: {memory.current_step}")
    except Exception as e:
        print(f"   ❌ Load failed: {e}")
        return False

    # 6. Test Vector Search
    print("\n6. Testing Vector Search...")
    try:
        query_embedding = [0.1] * 768  # Same as test embedding for similarity
        search_results = await search_memory_by_vector(query_embedding, limit=3)
        print(f"   Found {len(search_results)} results")

        if search_results:
            result = search_results[0]
            print(f"   Best match score: {result.get('score', 'N/A')}")
            print(f"   Content: {result['document']['content'][:50]}...")
    except Exception as e:
        print(f"   ❌ Vector search failed: {e}")
        return False

    # 7. Test GlobalMemory Integration
    print("\n7. Testing GlobalMemory Integration...")
    try:
        global_memory = GlobalMemory()

        # Add memory with persistence
        memory_id = await global_memory.add_memory(
            content="Global memory test content for persistence validation.",
            agent="global-test-agent",
            current_step=OrchestrationStep.TASK_EXECUTION,
            task_id="global-test-task",
            metadata={"global": True},
            tags=["global", "test"],
            state_id=test_state_id
        )
        print(f"   Added global memory: {memory_id}")

        # Retrieve memories
        retrieved = await global_memory.retrieve_memory(
            state_id=test_state_id,
            limit=5
        )
        print(f"   Retrieved {len(retrieved)} memories from global memory")

    except Exception as e:
        print(f"   ❌ GlobalMemory test failed: {e}")
        return False
    
    print("\n✅ All memory operations tests passed!")
    return True

async def cleanup_test_data():
    """Clean up test data."""
    print("\n🧹 Cleaning up test data...")
    try:
        from backend.app.core.db.database import get_db_context
        from sqlalchemy import text
        
        with get_db_context() as db:
            # Delete test memories and states
            db.execute(text("""
                DELETE FROM orchestration_memory
                WHERE state_id = 'test-state-001'
                OR agent LIKE '%test%'
            """))
            db.execute(text("""
                DELETE FROM orchestration_states
                WHERE id = 'test-state-001'
            """))
            db.commit()
            print("   Test data cleaned up successfully")
    except Exception as e:
        print(f"   ⚠️ Cleanup failed: {e}")

if __name__ == "__main__":
    async def main():
        try:
            success = await test_memory_operations()
            await cleanup_test_data()
            
            if success:
                print("\n🎉 Memory database operations are working correctly!")
                sys.exit(0)
            else:
                print("\n❌ Memory database operations have issues!")
                sys.exit(1)
                
        except Exception as e:
            logger.error(f"Test failed with exception: {e}")
            await cleanup_test_data()
            sys.exit(1)
    
    asyncio.run(main())
